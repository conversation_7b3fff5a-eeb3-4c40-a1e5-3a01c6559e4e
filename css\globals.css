:root {
    /* Theme Colors */
    --primary-color: #000000;
    --secondary-color: #dadada;
    --accent-color: #9ae99e;
    
    /* Typography */
    --ITC-font: "ITC";

    /* Easing  */
    --ease: cubic-bezier(0.66, 0, 0.34, 1);

    /* Spacings */
    --space-xs: 0.5rem;
    --space-sm: 1rem;
    --space-md: 1.5rem;
    --space-lg: 2rem;
    --space-xl: 3rem;
    --space-2xl: 4rem;
    --space-3xl: 6rem;
}

@font-face {
  font-family: NeueMontreal;
  src: url(fonts/NeueMontreal-Regular.otf);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: var(--bg);
  color: var(--fg);
  width: 100vw;
  height: 100svh;
  overflow: hidden;
}

#main{
  width: 100%;
  overflow-x: hidden;
}

img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

h1,
h2,
h3,
h4,
h5,
h6,
p,
span,
a,
button,
input,
textarea,
label,
select,
option {
  font-family: NeueMontreal;
}

.inversion-lens {
  position: relative;
  width: 100vw;
  height: 100svh;
  overflow: hidden;
}

.inversion-lens img {
  display: none;
}


 /* split text  */  
.lineParent {
    overflow: hidden;
    max-width: max-content;
    height: auto;
}

/* button */
.btn-cta {
    position: relative;
    display: inline-block;
    padding: 0.8rem 1.5rem;
    margin-top: 1rem;
    border-radius: 1000px;
    font-weight: 100;
    font-size:1.3em;
    line-height: 110%;
    text-transform: uppercase;
    transition: transform .3s;
    overflow: hidden;
    color: var(--secondary-color);
    text-decoration: none;
  }
  
  
  .btn-cta:hover {
    transform: scaleX(1.02);
    transition: transform .6s cubic-bezier(.34, 5.56, .64, 1);
  }
  
  
  .btn-cta-border {
    content: "";
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 1px;
    z-index: 3;
    border: 1px solid;
    border-radius: 1000px;
  }
  
  .btn-cta-ripple {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
    transform: translateZ(0);
    border-radius: inherit;
  }
  
  .btn-cta-ripple span {
    display: block;
    width: 100%;
    height: 100%;
    transform: translateY(101%);
    background: currentColor;
    border-radius: 50% 50% 0 0;
    transition: transform .5s cubic-bezier(.4, 0, 0, 1), border-radius .5s cubic-bezier(.4, 0, 0, 1);
  }
  
  .btn-cta:hover .btn-cta-ripple span {
    border-radius: 0;
    transform: translateY(0);
    transition-duration: .5s, .9s;
  }
  
  .btn-cta-title {
    position: relative;
    display: block;
    padding: 0 .16em 0 0;
    overflow: hidden;
    z-index: 2;
  }
  
  .btn-cta-title span {
    display: block;
    transition: transform .8s cubic-bezier(.16, 1, .3, 1);
  }
  
  .btn-cta-title span:after {
    content: attr(data-text);
    display: block;
    position: absolute;
    top: 110%;
    left: 0;
    color: var(--primary-color);
  }
  
  .btn-cta-title span::selection{
    background: var(--secondary-color);
    color: var(--primary-color);
  }
  
  .btn-cta:hover .btn-cta-title span {
    transform: translateY(-110%);
  }
  
